import { Client } from "minio";

const EXPIRE = 60 * 60 * 24;

export const minio = new Client({
  endPoint: process.env.S3_ENDPOINT as string,
  port: Number.parseInt(process.env.S3_PORT as string, 10),
  useSSL: false,
  accessKey: process.env.S3_ROOT_PASSWORD as string,
  secretKey: process.env.S3_ROOT_PASSWORD as string,
});

export const createBucket = async (bucketName: string): Promise<void> => {
  if (!(await minio.bucketExists(bucketName))) 
    await minio.makeBucket(bucketName, "eu-east-01");
}

export const getSignedAvatarUrl = async (bucket: string, object: string): Promise<string | null> => {
  if (!object || ! bucket) 
    return null;
  try {
    await createBucket(bucket);
    return await minio.presignedGetObject(bucket, object, EXPIRE);
  } catch (err) {
    console.error("Error generating signed URL:", err);
    return null;
  }
}

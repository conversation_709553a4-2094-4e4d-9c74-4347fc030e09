import { Client } from "minio";

const EXPIRE = 60 * 60 * 24;

export const minio = new Client({
  endPoint: process.env.S3_ENDPOINT || "localhost",
  port: Number.parseInt(process.env.S3_PORT as string, 10),
  useSSL: false,
  accessKey: process.env.S3_ROOT_USER as string,
  secretKey: process.env.S3_ROOT_PASSWORD as string,
});

export const createBucket = async (bucketName: string): Promise<void> => {
  if (!(await minio.bucketExists(bucketName))) await minio.makeBucket(bucketName, "eu-east-01");
};

export const getSignedAvatarUrl = async (bucket: string, object: string): Promise<string | null> => {
  if (!object || !bucket) return null;
  try {
    await createBucket(bucket);
    return await minio.presignedGetObject(bucket, object, EXPIRE);
  } catch (err) {
    console.error("Error generating signed URL:", err);
    return null;
  }
};

export const uploadProfilePicture = async (userId: string, file: Buffer, fileName: string, contentType: string): Promise<string | null> => {
  const bucket = "avatars";
  const objectName = `${userId}/${fileName}`;
  try {
    await createBucket(bucket);\
    await minio.putObject(bucket, objectName, file, file.length, {
      "Content-Type": contentType,
    });
    return objectName;
  } catch (err) {
    console.error("Error uploading profile picture:", err);
    return null;
  }
};

export const deleteProfilePicture = async (objectName: string): Promise<boolean> => {
  const bucket = "avatars";
  try {
    await minio.removeObject(bucket, objectName);
    return true;
  } catch (err) {
    console.error("Error deleting profile picture:", err);
    return false;
  }
};

export const getProfilePictureUrl = async (objectName?: string): Promise<string | null> => {
  if (!objectName)
    return null;
  return await getSignedAvatarUrl("avatars", objectName);
};

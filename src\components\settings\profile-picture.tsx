"use client";

import { useState, useRef, useEffect } from "react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/components/ui/toast";
import { Upload, User } from "lucide-react";
import { useSession } from "@/hooks/use-session";
import { uploadProfilePictureAction, getProfilePictureUrlAction } from "@/lib/actions/profile-picture";

type ProfilePictureData = {
  url: string | null;
  source: "minio" | "oauth" | null;
  hasCustomAvatar: boolean;
};

export const ProfilePicture = ({ isPrivate = false }: { isPrivate?: boolean }) => {
  const { user, refreshSession } = useSession();
  const [profilePicture, setProfilePicture] = useState<ProfilePictureData>({
    url: null,
    source: null,
    hasCustomAvatar: false,
  });
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch current profile picture
  const fetchProfilePicture = async () => {
    try {
      setLoading(true);
      const data = await getProfilePictureUrlAction(isPrivate);
      setProfilePicture(data);
    } catch (error) {
      console.error("Failed to fetch profile picture:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchProfilePicture();
    }
  }, [user]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleUpload(file);
    }
  };

  const handleUpload = async (file: File) => {
    try {
      setUploading(true);

      const formData = new FormData();
      formData.append("file", file);

      const result = await uploadProfilePictureAction(formData);

      if (result.success) {
        toast.success("Profile picture updated successfully!");
        await fetchProfilePicture();
        await refreshSession();
      } else {
        toast.error(result.error || "Failed to upload profile picture");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Failed to upload profile picture");
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  if (!user) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Picture</CardTitle>
        <CardDescription>Upload a custom profile picture or use your OAuth provider avatar</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-4">
          <Avatar className="size-20">
            {loading ? (
              <AvatarFallback>
                <div className="animate-pulse bg-muted rounded-none size-full" />
              </AvatarFallback>
            ) : (
              <>
                <AvatarImage src={profilePicture.url || undefined} alt={user.name || "Profile picture"} />
                <AvatarFallback>
                  <User className="size-8" />
                </AvatarFallback>
              </>
            )}
          </Avatar>

          <div className="flex-1">
            <p className="text-sm font-medium">{user.name}</p>
            <p className="text-sm text-muted-foreground">{user.email}</p>
            {profilePicture.source && (
              <p className="text-xs text-muted-foreground mt-1">
                Source: {profilePicture.source === "minio" ? "Custom upload" : "OAuth provider"}
              </p>
            )}
          </div>
        </div>

        <div className="flex gap-2">
          <Button onClick={handleUploadClick} disabled={uploading} className="flex items-center gap-2">
            <Upload className="size-4" />
            {uploading ? "Uploading..." : "Upload New Picture"}
          </Button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/jpg,image/png,image/webp"
          onChange={handleFileSelect}
          className="hidden"
        />

        <div className="text-xs text-muted-foreground">
          <p>• Supported formats: JPEG, PNG, WebP</p>
          <p>• Maximum file size: 5MB</p>
          <p>• Recommended size: 400x400 pixels</p>
        </div>
      </CardContent>
    </Card>
  );
};

# Profile Picture Upload System

This document describes the profile picture upload system implemented for the Mystique application.

## Overview

The system allows users to upload custom profile pictures to MinIO storage while maintaining compatibility with OAuth provider avatars (Google, GitHub). Users can see their current avatar (either from OAuth or custom upload) and upload new pictures through the `/settings` page.

## Features

- **OAuth Avatar Support**: Automatically displays avatars from Google/GitHub OAuth providers
- **Custom Upload**: Users can upload JPEG, PNG, or WebP images up to 5MB
- **MinIO Storage**: Profile pictures are stored in MinIO with organized bucket structure
- **Signed URLs**: Secure access to uploaded images through time-limited signed URLs
- **Fallback Display**: Shows user initials when no avatar is available
- **Real-time Updates**: Profile picture changes are reflected immediately

## Architecture

### Components

1. **ProfilePicture Component** (`src/components/settings/profile-picture.tsx`)
   - Main UI component for displaying and uploading profile pictures
   - Handles file selection, upload progress, and error states
   - Shows current avatar with source indication (OAuth vs custom)

2. **useSession Hook** (`src/hooks/use-session.ts`)
   - Custom hook for managing user session state
   - Provides current user data and session refresh functionality

### API Endpoints

1. **Upload Endpoint** (`/api/profile-picture/upload`)
   - Handles file upload to MinIO
   - Validates file type and size
   - Updates user record with new image path
   - Returns success/error status

2. **URL Endpoint** (`/api/profile-picture/url`)
   - Retrieves current profile picture URL
   - Generates signed URLs for MinIO objects
   - Returns OAuth URLs directly
   - Indicates image source (MinIO vs OAuth)

### MinIO Integration

**Configuration** (`src/lib/minio.ts`):
- Client setup with environment variables
- Bucket management (auto-creation)
- File upload with metadata
- Signed URL generation
- Object deletion capabilities

**Storage Structure**:
```
avatars/
├── {userId}/
│   ├── avatar-{timestamp}.jpg
│   ├── avatar-{timestamp}.png
│   └── ...
```

## Database Schema

The existing `user` table includes an `image` field that stores:
- OAuth provider avatar URLs (starting with `http`)
- MinIO object names for custom uploads (e.g., `userId/avatar-timestamp.jpg`)

## Environment Variables

Required environment variables for MinIO:
```env
S3_ENDPOINT=localhost
S3_PORT=9000
S3_ROOT_USER=minioadmin
S3_ROOT_PASSWORD=minioadmin
```

## File Validation

- **Allowed Types**: JPEG, PNG, WebP
- **Maximum Size**: 5MB
- **Recommended Dimensions**: 400x400 pixels

## Security

- Authentication required for all profile picture operations
- File type validation prevents malicious uploads
- Size limits prevent storage abuse
- Signed URLs provide secure, time-limited access
- User isolation through folder structure

## Usage

1. Navigate to `/settings` page
2. Current profile picture is displayed with source indication
3. Click "Upload New Picture" to select a file
4. File is automatically uploaded and validated
5. Success/error feedback is provided via toast notifications
6. Profile picture updates immediately upon successful upload

## Error Handling

- Invalid file types are rejected with clear error messages
- File size limits are enforced
- Network errors are caught and displayed to users
- MinIO connection issues are logged server-side
- Fallback to user initials when no image is available

## Future Enhancements

- Image resizing/optimization before upload
- Multiple image format support
- Crop/edit functionality
- Avatar deletion option
- Image compression for better performance

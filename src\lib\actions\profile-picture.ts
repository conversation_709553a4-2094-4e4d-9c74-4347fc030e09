"use server";

import { auth } from "@/lib/auth";
import { uploadProfilePicture, getProfilePictureUrl, getSignedProfilePictureUrl } from "@/lib/minio";
import { db } from "@/db";
import { user } from "@/db/schema";
import { eq } from "drizzle-orm";
import { headers } from "next/headers";

type ProfilePictureData = {
  url: string | null;
  source: "minio" | "oauth" | null;
  hasCustomAvatar: boolean;
};

export const uploadProfilePictureAction = async (formData: FormData) => {
  try {
    // Get session from Better Auth
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return { error: "Unauthorized", success: false };
    }

    const file = formData.get("file") as File;

    if (!file) {
      return { error: "No file provided", success: false };
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      return {
        error: "Invalid file type. Only JPEG, PNG, and WebP are allowed.",
        success: false,
      };
    }

    // Validate file size (5MB max)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        error: "File too large. Maximum size is 5MB.",
        success: false,
      };
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Generate unique filename
    const timestamp = Date.now();
    const extension = file.name.split(".").pop() || "jpg";
    const fileName = `avatar-${timestamp}.${extension}`;

    // Upload to MinIO
    const objectName = await uploadProfilePicture(session.user.id, buffer, fileName, file.type);

    if (!objectName) {
      return {
        error: "Failed to upload file",
        success: false,
      };
    }

    // Update user record with new image path
    await db
      .update(user)
      .set({
        image: objectName,
        updatedAt: new Date(),
      })
      .where(eq(user.id, session.user.id));

    return {
      success: true,
      objectName,
      message: "Profile picture uploaded successfully",
    };
  } catch (error) {
    console.error("Profile picture upload error:", error);
    return {
      error: "Internal server error",
      success: false,
    };
  }
};

export const getProfilePictureUrlAction = async (isPrivate: boolean = false): Promise<ProfilePictureData> => {
  try {
    // Get session from Better Auth
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return {
        url: null,
        source: null,
        hasCustomAvatar: false,
      };
    }

    // Get user data from database
    const userData = await db.select().from(user).where(eq(user.id, session.user.id)).limit(1);

    if (!userData.length) {
      return {
        url: null,
        source: null,
        hasCustomAvatar: false,
      };
    }

    const currentUser = userData[0];

    // If user has a MinIO image, get URL (signed or public based on isPrivate)
    if (currentUser.image && !currentUser.image.startsWith("http")) {
      const avatarUrl = isPrivate 
        ? await getSignedProfilePictureUrl(currentUser.image)
        : getProfilePictureUrl(currentUser.image);
      
      return {
        url: avatarUrl,
        source: "minio",
        hasCustomAvatar: true,
      };
    }

    // If user has OAuth image, return it directly
    if (currentUser.image && currentUser.image.startsWith("http")) {
      return {
        url: currentUser.image,
        source: "oauth",
        hasCustomAvatar: false,
      };
    }

    // No image available
    return {
      url: null,
      source: null,
      hasCustomAvatar: false,
    };
  } catch (error) {
    console.error("Get profile picture URL error:", error);
    return {
      url: null,
      source: null,
      hasCustomAvatar: false,
    };
  }
};

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { getProfilePictureUrl } from "@/lib/minio";
import { db } from "@/db";
import { user } from "@/db/schema";
import { eq } from "drizzle-orm";

export const GET = async (request: NextRequest) => {
  try {
    // Get session from Better Auth
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user data from database
    const userData = await db
      .select()
      .from(user)
      .where(eq(user.id, session.user.id))
      .limit(1);

    if (!userData.length) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const currentUser = userData[0];
    
    // If user has a MinIO image, get signed URL
    if (currentUser.image && !currentUser.image.startsWith('http')) {
      const signedUrl = await getProfilePictureUrl(currentUser.image);
      return NextResponse.json({
        url: signedUrl,
        source: 'minio',
        hasCustomAvatar: true
      });
    }
    
    // If user has OAuth image, return it directly
    if (currentUser.image && currentUser.image.startsWith('http')) {
      return NextResponse.json({
        url: currentUser.image,
        source: 'oauth',
        hasCustomAvatar: false
      });
    }

    // No image available
    return NextResponse.json({
      url: null,
      source: null,
      hasCustomAvatar: false
    });

  } catch (error) {
    console.error("Get profile picture URL error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
};
